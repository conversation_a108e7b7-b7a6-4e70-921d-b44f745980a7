import cors from 'cors';
import dotenv from 'dotenv';
import express from 'express';
import session from 'express-session';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import UserInfo from './models/userInfoModel.js';
import User from './models/userModel.js';
// Import routes
import { findOrCreateGoogleUser } from './controller/authController.js';
import routes from './routes/index.js';
const app = express();
const PORT = process.env.PORT || 3000;
// Load environment variables
dotenv.config();

const MONGO_URI = process.env.MONGO_URI;
const JWT_SECRET_KEY = process.env.JWT_SECRET_KEY;

app.use(cors());
app.use(express.json());
app.use('/api', routes);

app.use(session({ secret: 'your_secret', resave: false, saveUninitialized: true }));
app.use(passport.initialize());
app.use(passport.session());


passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: 'http://localhost:3000/api/auth/google/callback'
}, async (accessToken, refreshToken, profile, done) => {
  try {
    const user = await findOrCreateGoogleUser(profile);
    return done(null, user);
  } catch (err) {
    return done(err, null);
  }
}));

passport.serializeUser((user, done) => done(null, user.id));
passport.deserializeUser(async (id, done) => {
  const user = await User.findById(id);
  done(null, user);
});

// Routes
app.get('/api/auth/google', (req, res, next) => {
  // Check if this is a mobile request
  const isMobile = req.query.mobile === 'true';
  const state = req.query.state;
  const redirectUri = req.query.redirect_uri;

  // Store mobile-specific data in session
  if (isMobile) {
    req.session.isMobile = true;
    req.session.mobileState = state;
    req.session.mobileRedirectUri = redirectUri;
  }

  passport.authenticate('google', { scope: ['profile', 'email'] })(req, res, next);
});

app.get('/api/auth/google/callback',
  passport.authenticate('google', { failureRedirect: '/auth/google/failure' }),
  async (req, res) => {
    console.log('Google callback hit, authentication successful');
    try {
      const user = req.user;
      const isMobile = req.session.isMobile;
      const mobileState = req.session.mobileState;
      const mobileRedirectUri = req.session.mobileRedirectUri;

      // Generate JWT token
      const token = jwt.sign({ id: user._id }, process.env.JWT_SECRET_KEY, { expiresIn: '24h' });

      // Check if user has complete profile (has phone number)
      const userInfo = await UserInfo.findOne({ userId: user._id });

      if (isMobile) {
        // Mobile app redirect
        const redirectUri = mobileRedirectUri || 'murai-mobile://auth';

        if (userInfo && userInfo.phoneNumber) {
          // User has complete profile
          const params = new URLSearchParams({
            token,
            user: JSON.stringify({
              id: user._id,
              email: user.email,
              name: user.name,
              firstName: user.firstName,
              lastName: user.lastName,
              isVerified: user.isVerified,
              provider: 'google'
            }),
            state: mobileState || '',
            success: 'true'
          }).toString();

          res.redirect(`${redirectUri}?${params}`);
        } else {
          // User needs to complete profile - for mobile, we'll still redirect with token
          // and let the mobile app handle profile completion
          const params = new URLSearchParams({
            token,
            user: JSON.stringify({
              id: user._id,
              email: user.email,
              name: user.name,
              firstName: user.firstName,
              lastName: user.lastName,
              isVerified: user.isVerified,
              provider: 'google',
              needsProfileCompletion: true
            }),
            state: mobileState || '',
            success: 'true'
          }).toString();

          res.redirect(`${redirectUri}?${params}`);
        }

        // Clear mobile session data
        delete req.session.isMobile;
        delete req.session.mobileState;
        delete req.session.mobileRedirectUri;
      } else {
        // Web app redirect (existing logic)
        if (userInfo && userInfo.phoneNumber) {
          // User has complete profile, redirect to client dashboard
          const params = new URLSearchParams({
            token,
            email: user.email,
            name: user.name
          }).toString();
          res.redirect(`http://localhost:5173/client/dashboard?${params}`);
        } else {
          // User needs to complete profile
          const email = user.email;
          const firstName = user.firstName || '';
          const lastName = user.lastName || '';
          const params = new URLSearchParams({
            email,
            firstName,
            lastName
          }).toString();
          res.redirect(`http://localhost:5173/complete-profile?${params}`);
        }
      }
    } catch (error) {
      console.error('Error in Google callback:', error);

      if (req.session.isMobile) {
        const redirectUri = req.session.mobileRedirectUri || 'murai-mobile://auth';
        const params = new URLSearchParams({
          error: 'authentication_failed',
          state: req.session.mobileState || ''
        }).toString();
        res.redirect(`${redirectUri}?${params}`);
      } else {
        res.redirect('http://localhost:5173/login?error=authentication_failed');
      }
    }
  }
);

app.get('/auth/google/failure', (req, res) => {
  res.status(401).send('Google authentication failed. Please try again or contact support.');
});

// Route to get user info with token (for mobile app)
app.get('/api/auth/me', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY);
    const user = await User.findById(decoded.id);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get additional user info
    const userInfo = await UserInfo.findOne({ userId: user._id });

    res.json({
      id: user._id,
      email: user.email,
      name: user.name,
      firstName: user.firstName,
      lastName: user.lastName,
      isVerified: user.isVerified,
      hasCompleteProfile: !!(userInfo && userInfo.phoneNumber),
      userInfo: userInfo ? {
        phoneNumber: userInfo.phoneNumber,
        address: userInfo.address,
        dateOfBirth: userInfo.dateOfBirth
      } : null
    });
  } catch (error) {
    console.error('Error getting user info:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
});

async function connectDB() {
  try {
    await mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

connectDB();

app.get('/', (req, res) => {
  res.send('Murai Server is running');
}
);

app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});