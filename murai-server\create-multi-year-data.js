import dotenv from 'dotenv';
import mongoose from 'mongoose';
import DetectedWord from './models/detectedWordModel.js';
import User from './models/userModel.js';

dotenv.config();

const MONGO_URI = process.env.MONGO_URI;

async function connectDB() {
  try {
    await mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

async function createMultiYearData() {
  console.log('🚀 Creating multi-year detection data...\n');

  try {
    // Find the test user
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      console.log('❌ Test user not found. Please run create-test-user-with-data.js first');
      return;
    }

    console.log('✅ Found test user:', testUser.name);
    const userId = testUser._id;

    // Create data for 2023, 2024, and 2025
    const years = [2023, 2024, 2025];
    const websites = ['facebook.com', 'twitter.com', 'instagram.com', 'youtube.com', 'tiktok.com', 'reddit.com'];
    const harmfulWords = ['spam', 'scam', 'fake', 'virus', 'malware', 'phishing', 'fraud', 'hack'];

    for (const year of years) {
      console.log(`\n📅 Creating data for ${year}...`);
      
      // Create 50-100 detections per year
      const detectionsCount = Math.floor(Math.random() * 51) + 50; // 50-100
      
      for (let i = 0; i < detectionsCount; i++) {
        // Random date within the year
        const startDate = new Date(year, 0, 1);
        const endDate = new Date(year, 11, 31);
        const randomDate = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));
        
        const website = websites[Math.floor(Math.random() * websites.length)];
        const detection = new DetectedWord({
          userId: userId,
          word: harmfulWords[Math.floor(Math.random() * harmfulWords.length)],
          context: `Detected harmful content on ${website}`,
          url: `https://${website}/post/${Math.floor(Math.random() * 10000)}`,
          sentimentScore: Math.random() * 100, // 0-100
          accuracy: 0.8 + Math.random() * 0.2, // 0.8-1.0
          responseTime: Math.floor(Math.random() * 500) + 50, // 50-550ms
          severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
          patternType: ['Profanity', 'Hate Speech', 'Spam', 'Scam'][Math.floor(Math.random() * 4)],
          language: 'English',
          siteType: 'Social Media',
          createdAt: randomDate
        });
        
        await detection.save();
      }
      
      console.log(`✅ Created ${detectionsCount} detections for ${year}`);
    }

    // Get summary of data by year
    console.log('\n📊 Data Summary:');
    for (const year of years) {
      const count = await DetectedWord.countDocuments({
        userId: userId,
        createdAt: {
          $gte: new Date(year, 0, 1),
          $lt: new Date(year + 1, 0, 1)
        }
      });
      console.log(`   ${year}: ${count} detections`);
    }

    console.log('\n✅ Multi-year data creation completed successfully!');
    console.log('\n🎯 Now the year selector should show multiple years:');
    console.log('   - Overall (all years combined)');
    console.log('   - 2023');
    console.log('   - 2024'); 
    console.log('   - 2025');

  } catch (error) {
    console.error('❌ Error creating multi-year data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the script
connectDB().then(() => {
  createMultiYearData();
});
