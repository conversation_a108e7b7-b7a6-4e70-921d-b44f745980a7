import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3000/api';

async function testMobileAuth() {
  console.log('🧪 Testing Mobile App Authentication...\n');

  // Test login with the test user credentials
  try {
    console.log('1. Testing login with test user credentials');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        email: '<EMAIL>', 
        password: 'user123' 
      })
    });

    const loginData = await loginResponse.json();
    console.log('Login Status:', loginResponse.status);
    console.log('Login Response:', JSON.stringify(loginData, null, 2));

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginData.token;
    console.log('✅ Login successful, token received');
    console.log('---\n');

    // Test dashboard endpoints with the token
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    // Test available years
    console.log('2. Testing /user-dashboard/available-years with mobile token');
    const yearsResponse = await fetch(`${API_BASE_URL}/user-dashboard/available-years`, {
      headers
    });
    const yearsData = await yearsResponse.json();
    console.log('Status:', yearsResponse.status);
    console.log('Response:', JSON.stringify(yearsData, null, 2));
    console.log('---\n');

    // Test overview
    console.log('3. Testing /user-dashboard/overview?timeRange=day with mobile token');
    const overviewResponse = await fetch(`${API_BASE_URL}/user-dashboard/overview?timeRange=day`, {
      headers
    });
    const overviewData = await overviewResponse.json();
    console.log('Status:', overviewResponse.status);
    console.log('Response:', JSON.stringify(overviewData, null, 2));
    console.log('---\n');

    // Test activity chart
    console.log('4. Testing /user-dashboard/activity-chart?timeRange=day with mobile token');
    const chartResponse = await fetch(`${API_BASE_URL}/user-dashboard/activity-chart?timeRange=day`, {
      headers
    });
    const chartData = await chartResponse.json();
    console.log('Status:', chartResponse.status);
    console.log('Response:', JSON.stringify(chartData, null, 2));
    console.log('---\n');

    console.log('✅ All tests completed successfully!');
    console.log('\n📱 Mobile app should be able to use these credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: user123');

  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

testMobileAuth();
