import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';
import fetch from 'node-fetch';

dotenv.config();

const API_BASE_URL = 'http://localhost:3000/api';
const JWT_SECRET_KEY = process.env.JWT_SECRET_KEY;

// Create a test user token
const testUserToken = jwt.sign(
  { 
    id: '6882f61cc328075458401c59',
    role: 'user',
    email: '<EMAIL>'
  }, 
  JWT_SECRET_KEY, 
  { expiresIn: '1h' }
);

async function testNewTimeRanges() {
  console.log('🧪 Testing New User-Friendly Time Ranges...\n');

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testUserToken}`
  };

  const timeRanges = ['today', 'week', 'month', 'year'];
  const friendlyNames = ['Today', 'Last 7 Days', 'Last Month', 'Last Year'];

  for (let i = 0; i < timeRanges.length; i++) {
    const timeRange = timeRanges[i];
    const friendlyName = friendlyNames[i];
    
    console.log(`📅 Testing ${friendlyName} (${timeRange})`);
    console.log('='.repeat(50));

    // Test overview
    try {
      const overviewResponse = await fetch(`${API_BASE_URL}/user-dashboard/overview?timeRange=${timeRange}`, {
        headers
      });
      const overviewData = await overviewResponse.json();
      console.log('✅ Overview:', JSON.stringify(overviewData, null, 2));
    } catch (error) {
      console.log('❌ Overview Error:', error.message);
    }

    // Test activity chart
    try {
      const chartResponse = await fetch(`${API_BASE_URL}/user-dashboard/activity-chart?timeRange=${timeRange}`, {
        headers
      });
      const chartData = await chartResponse.json();
      console.log('✅ Chart Labels:', chartData.labels);
      console.log('✅ Chart Data Points:', chartData.datasets[0].data.length);
      console.log('✅ Sample Data:', chartData.datasets[0].data.slice(0, 5));
    } catch (error) {
      console.log('❌ Chart Error:', error.message);
    }

    console.log('\n');
  }

  console.log('🎯 Summary:');
  console.log('- Today: Shows 24 hours (00:00 to 23:00) - Data x100');
  console.log('- Last 7 Days: Shows 7 days with day names - Data x100');
  console.log('- Last Month: Shows 12 months (no dates) - Data x100');
  console.log('- Last Year: Shows years (2023, 2024, 2025, etc.) - Data x100');
}

testNewTimeRanges();
