import React from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { COLORS, SPACING } from '../../constants/theme';

export default function LoadingSpinner({ 
  size = 'large', 
  color = COLORS.PRIMARY, 
  text = 'Loading...', 
  showText = true,
  style = {} 
}) {
  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={color} />
      {showText && (
        <Text style={styles.text}>{text}</Text>
      )}
    </View>
  );
}

const styles = {
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.lg,
  },
  text: {
    marginTop: SPACING.md,
    color: COLORS.TEXT_MUTED,
    fontSize: 16,
    fontFamily: 'Poppins-Regular',
    textAlign: 'center',
  },
};
