import { Ionicons } from "@expo/vector-icons";
import { Link, useRouter } from "expo-router";
import { useState } from "react";
import {
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StatusBar,
    Text,
    TouchableOpacity,
    View
} from "react-native";
import AppButton from "../../components/common/AppButton";
import AppInput from "../../components/common/AppInput";
import { BORDER_RADIUS, COLORS, SHADOWS, SPACING } from "../../constants/theme";
import { useAuth } from "../../context/AuthContext";

export default function Register() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [acceptTerms, setAcceptTerms] = useState(false);

  const { register, registerWithGoogle } = useAuth();
  const router = useRouter();

  const validateForm = () => {
    if (!name.trim()) {
      setError("Please enter your name");
      return false;
    }
    if (!email.trim()) {
      setError("Please enter your email");
      return false;
    }
    if (!password) {
      setError("Please enter a password");
      return false;
    }
    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      return false;
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return false;
    }
    if (!acceptTerms) {
      setError("Please accept the terms and conditions");
      return false;
    }
    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError("");

    try {
      await register(email, password, name);
      // The register function will handle navigation
    } catch (err) {
      setError(err.message || "Registration failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleRegister = async () => {
    try {
      setGoogleLoading(true);
      setError("");

      const result = await registerWithGoogle();

      if (result.success) {
        console.log('✅ Google registration successful');
        router.replace("/(app)");
      } else {
        setError("Google authentication failed");
      }
    } catch (err) {
      console.error('❌ Google registration error:', err);
      setError(err.message || "Google sign-up failed. Please try again.");
    } finally {
      setGoogleLoading(false);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: COLORS.BG }}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.BG} />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.container}>
            {/* Header Section */}
            <View style={styles.header}>
              <Image
                source={require("../../../assets/images/logo.png")}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>
                Join Murai to get started
              </Text>
            </View>

            {/* Error Message */}
            {error ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={20} color={COLORS.ERROR} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : null}

            {/* Registration Form */}
            <View style={styles.formContainer}>
              <AppInput
                value={name}
                onChangeText={(text) => {
                  setName(text);
                  setError("");
                }}
                placeholder="Enter your full name"
                autoComplete="name"
                style={styles.input}
                leftIcon={
                  <Ionicons name="person-outline" size={20} color={COLORS.TEXT_MUTED} />
                }
              />

              <AppInput
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  setError("");
                }}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                style={styles.input}
                leftIcon={
                  <Ionicons name="mail-outline" size={20} color={COLORS.TEXT_MUTED} />
                }
              />

              <AppInput
                value={password}
                onChangeText={(text) => {
                  setPassword(text);
                  setError("");
                }}
                placeholder="Create a password"
                secureTextEntry={!showPassword}
                autoComplete="password-new"
                style={styles.input}
                leftIcon={
                  <Ionicons name="lock-closed-outline" size={20} color={COLORS.TEXT_MUTED} />
                }
                rightIcon={
                  <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                    <Ionicons
                      name={showPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color={COLORS.TEXT_MUTED}
                    />
                  </TouchableOpacity>
                }
              />

              <AppInput
                value={confirmPassword}
                onChangeText={(text) => {
                  setConfirmPassword(text);
                  setError("");
                }}
                placeholder="Confirm your password"
                secureTextEntry={!showConfirmPassword}
                autoComplete="password-new"
                style={styles.input}
                leftIcon={
                  <Ionicons name="lock-closed-outline" size={20} color={COLORS.TEXT_MUTED} />
                }
                rightIcon={
                  <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
                    <Ionicons
                      name={showConfirmPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color={COLORS.TEXT_MUTED}
                    />
                  </TouchableOpacity>
                }
              />

              {/* Terms and Conditions */}
              <TouchableOpacity
                style={styles.termsContainer}
                onPress={() => setAcceptTerms(!acceptTerms)}
              >
                <Ionicons
                  name={acceptTerms ? "checkbox" : "square-outline"}
                  size={20}
                  color={acceptTerms ? COLORS.PRIMARY : COLORS.TEXT_MUTED}
                />
                <Text style={styles.termsText}>
                  I agree to the{" "}
                  <Text style={styles.termsLink}>Terms of Service</Text>
                  {" "}and{" "}
                  <Text style={styles.termsLink}>Privacy Policy</Text>
                </Text>
              </TouchableOpacity>

              {/* Register Button */}
              <AppButton
                title="Create Account"
                onPress={handleRegister}
                loading={loading}
                style={styles.registerButton}
              />

              {/* Divider */}
              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>or</Text>
                <View style={styles.dividerLine} />
              </View>

              {/* Google Register Button */}
              <TouchableOpacity
                style={styles.googleButton}
                onPress={handleGoogleRegister}
                disabled={googleLoading}
              >
                <Ionicons name="logo-google" size={20} color={COLORS.TEXT_MAIN} />
                <Text style={styles.googleButtonText}>
                  {googleLoading ? "Creating account..." : "Continue with Google"}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Sign In Link */}
            <View style={styles.signInContainer}>
              <Text style={styles.signInText}>Already have an account? </Text>
              <Link href="/login" style={styles.signInLink}>
                Sign In
              </Link>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.lg,
  },
  header: {
    alignItems: "center",
    marginBottom: SPACING.xl,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: SPACING.md,
    opacity: 0.95,
  },
  title: {
    color: COLORS.TEXT_MAIN,
    fontSize: 28,
    fontFamily: "Poppins-Bold",
    letterSpacing: 0.5,
    marginBottom: SPACING.xs,
    textAlign: "center",
  },
  subtitle: {
    color: COLORS.TEXT_MUTED,
    fontSize: 16,
    fontFamily: "Poppins-Regular",
    letterSpacing: 0.2,
    textAlign: "center",
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${COLORS.ERROR}15`,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: `${COLORS.ERROR}30`,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 14,
    fontFamily: "Poppins-Medium",
    marginLeft: SPACING.sm,
    flex: 1,
  },
  formContainer: {
    marginBottom: SPACING.lg,
  },
  input: {
    marginBottom: SPACING.md,
  },
  termsContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: SPACING.lg,
    paddingHorizontal: SPACING.xs,
  },
  termsText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: 14,
    fontFamily: "Poppins-Regular",
    marginLeft: SPACING.sm,
    flex: 1,
    lineHeight: 20,
  },
  termsLink: {
    color: COLORS.PRIMARY,
    fontFamily: "Poppins-Medium",
  },
  registerButton: {
    marginBottom: SPACING.lg,
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: SPACING.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.BORDER,
  },
  dividerText: {
    color: COLORS.TEXT_MUTED,
    fontSize: 14,
    fontFamily: "Poppins-Regular",
    marginHorizontal: SPACING.md,
  },
  googleButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: COLORS.CARD_BG,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    ...SHADOWS.sm,
  },
  googleButtonText: {
    color: COLORS.TEXT_MAIN,
    fontSize: 16,
    fontFamily: "Poppins-SemiBold",
    marginLeft: SPACING.sm,
  },
  signInContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: "auto",
  },
  signInText: {
    color: COLORS.TEXT_MUTED,
    fontSize: 16,
    fontFamily: "Poppins-Regular",
  },
  signInLink: {
    color: COLORS.PRIMARY,
    fontSize: 16,
    fontFamily: "Poppins-SemiBold",
  },
};