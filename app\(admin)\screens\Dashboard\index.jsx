import { Feather } from '@expo/vector-icons';
import { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    Modal,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MainHeader from '../../../components/common/MainHeader';

const API_BASE_URL = 'http://localhost:3000/api';

const dashboardService = {
  getOverview: async (timeRange) => {
    try {
      const response = await fetch(`${API_BASE_URL}/dashboard/overview?timeRange=${encodeURIComponent(timeRange)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      throw error;
    }
  },
  getActivityChart: async (timeRange) => {
    try {
      const response = await fetch(`${API_BASE_URL}/dashboard/activity-chart?timeRange=${encodeURIComponent(timeRange)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      throw error;
    }
  },
  getInsights: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/dashboard/insights`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      throw error;
    }
  },
};

const { width } = Dimensions.get('window');

export default function AdminDashboardScreen({ navigation }) {
  const [selectedTimeRange, setSelectedTimeRange] = useState('Today');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    overview: null,
    chartData: null,
    insights: null,
    topFlaggedContent: [],
    topMonitoredWebsites: [],
    keyUserActivity: [],
  });

  const defaultChartData = {
    labels: ['', '', '', '', '', '', ''],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0, 0],
        strokeWidth: 2,
        color: (opacity = 1) => `rgba(34, 197, 94, ${opacity})`,
        fillShadowGradient: 'rgba(34, 197, 94, 0.1)',
        fillShadowGradientOpacity: 0.1,
      },
      {
        data: [0, 0, 0, 0, 0, 0, 0],
        strokeWidth: 2,
        color: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
        fillShadowGradient: 'rgba(107, 114, 128, 0.1)',
        fillShadowGradientOpacity: 0.1,
      },
    ],
  };

  const chartConfig = {
    backgroundColor: 'transparent',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(156, 163, 175, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '0',
    },
    propsForBackgroundLines: {
      strokeWidth: 0,
    },
    withHorizontalLabels: false,
    withVerticalLabels: false,
    withInnerLines: false,
    withOuterLines: false,
  };

  const timeRanges = ['Today', 'Last 7 days', 'Last 30 days', 'All Time'];

  const fetchDashboardData = async (timeRange) => {
    try {
      setIsLoading(true);
      setError(null); // Clear any previous errors
      const [overview, chartData, insights] = await Promise.all([
        dashboardService.getOverview(timeRange),
        dashboardService.getActivityChart(timeRange),
        dashboardService.getInsights(),
      ]);

      // Placeholder for new analytics data - these would typically come from new API calls
      const topFlaggedContent = [
        { id: 1, term: 'badword', count: 120 },
        { id: 2, term: 'anotherbad', count: 90 },
        { id: 3, term: 'offensive', count: 75 },
      ];
      const topMonitoredWebsites = [
        { id: 1, url: 'socialmedia.com', issues: 50 },
        { id: 2, url: 'forum.net', issues: 35 },
        { id: 3, url: 'chat.app', issues: 20 },
      ];
      const keyUserActivity = [
        { id: 1, user: 'Alice', actions: 250 },
        { id: 2, user: 'Bob', actions: 180 },
        { id: 3, user: 'Charlie', actions: 150 },
      ];

      setDashboardData({
        overview,
        chartData,
        insights,
        topFlaggedContent,
        topMonitoredWebsites,
        keyUserActivity,
      });
    } catch (error) {
      console.error("Failed to fetch dashboard data:", error);
      setError("Failed to load dashboard data. Please try again later.");
      setDashboardData({
        overview: {
          harmfulContentDetected: { value: '0', change: '+0%' },
          websitesMonitored: { value: '0', change: '+0' },
          protectionEffectiveness: { value: '95%', change: '+0%' },
        },
        chartData: {
          labels: ['', '', '', '', '', '', ''],
          datasets: [
            { label: 'Protected', data: [0, 0, 0, 0, 0, 0, 0] },
            { label: 'Monitored', data: [0, 0, 0, 0, 0, 0, 0] },
          ],
        },
        insights: {
          insights: [
            { icon: 'shield-alert', text: 'Unable to load insights', color: '#ef4444' },
          ],
        },
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    fetchDashboardData(selectedTimeRange);
  };

  useEffect(() => {
    fetchDashboardData(selectedTimeRange);
  }, [selectedTimeRange]);

  // Detection and Reports counts mock data for each time range
  const detectionReportsData = {
    Today: {
      labels: ['12AM', '4AM', '8AM', '12PM', '4PM', '8PM'],
      detections: [5, 8, 12, 20, 15, 10],
      reports: [2, 3, 4, 6, 5, 3],
    },
    'Last 7 days': {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      detections: [45, 52, 38, 67, 89, 74, 92],
      reports: [12, 15, 10, 18, 20, 16, 22],
    },
    'Last 30 days': {
      labels: Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`),
      detections: Array.from({ length: 30 }, () => Math.floor(Math.random() * 100)),
      reports: Array.from({ length: 30 }, () => Math.floor(Math.random() * 30)),
    },
    'All Time': {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      detections: [320, 280, 350, 400, 420, 390, 410, 430, 390, 370, 360, 400],
      reports: [80, 70, 90, 100, 110, 95, 105, 120, 100, 90, 85, 110],
    },
  };

  const selectedData = detectionReportsData[selectedTimeRange];
  const detectionChartData = {
    labels: selectedData.labels,
    datasets: [
      {
        data: selectedData.detections,
        color: (opacity = 1) => `rgba(1, 185, 127, ${opacity})`,
        fillShadowGradient: 'rgba(1, 185, 127, 0.1)',
        fillShadowGradientOpacity: 0.1,
      },
      {
        data: selectedData.reports,
        color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
        fillShadowGradient: 'rgba(59, 130, 246, 0.1)',
        fillShadowGradientOpacity: 0.1,
      },
    ],
  };

  const overallStats = dashboardData.overview ? [
    {
      value: dashboardData.overview.harmfulContentDetected?.value || '0',
      label: 'HARMFUL CONTENT\nDETECTED',
      change: dashboardData.overview.harmfulContentDetected?.change || '+0%',
      color: '#6b7280',
      icon: 'shield-alert'
    },
    {
      value: dashboardData.overview.websitesMonitored?.value || '0',
      label: 'WEBSITES\nMONITORED',
      change: dashboardData.overview.websitesMonitored?.change || '+0',
      color: '#6b7280',
      icon: 'web'
    },
    {
      value: dashboardData.overview.protectionEffectiveness?.value || '95%',
      label: 'PROTECTION\nEFFECTIVENESS',
      change: dashboardData.overview.protectionEffectiveness?.change || '+0%',
      color: '#6b7280',
      icon: 'shield-check'
    },
  ] : [];

  const insightsData = dashboardData.insights?.insights || [
    { icon: 'shield-alert', text: 'Loading insights...', color: '#6b7280' },
  ];

  const menuOptions = [
    {
      icon: 'shield-alert',
      title: 'Detection',
      subtitle: 'Detection analytics and details',
      color: '#e8f5f0',
      iconColor: '#01B97F',
      screen: 'AdminDetection',
    },
    {
      icon: 'web',
      title: 'Sites',
      subtitle: 'Sites analytics and details',
      color: '#e8f5f0',
      iconColor: '#01B97F',
      screen: 'AdminSites',
    },
    {
      icon: 'translate',
      title: 'Languages',
      subtitle: 'Languages analytics and details',
      color: '#e8f5f0',
      iconColor: '#01B97F',
      screen: 'AdminLanguages',
    },
    {
      icon: 'account-group',
      title: 'Groups',
      subtitle: 'Groups analytics and details',
      color: '#e8f5f0',
      iconColor: '#01B97F',
      screen: 'AdminGroups',
    },
    {
      icon: 'chart-timeline-variant',
      title: 'Patterns Over Time',
      subtitle: 'Patterns over time analytics and details',
      color: '#e8f5f0',
      iconColor: '#01B97F',
      screen: 'AdminPatternsOverTime',
    },
  ];

  const sideMenuItems = [
    { title: 'Dashboard Overview', icon: 'bar-chart-2', action: () => setIsMenuOpen(false) },
    { title: 'Detection', icon: 'alert-circle', action: () => navigation.navigate('AdminDetection') },
    { title: 'Sites', icon: 'web', action: () => navigation.navigate('AdminSites') },
    { title: 'Languages', icon: 'translate', action: () => navigation.navigate('AdminLanguages') },
    { title: 'Groups', icon: 'account-group', action: () => navigation.navigate('AdminGroups') },
    { title: 'Patterns Over Time', icon: 'trending-up', action: () => navigation.navigate('AdminPatternsOverTime') },
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleMenuAction = (action) => {
    setIsMenuOpen(false);
    action();
  };

  const handleTimeRangeChange = (range) => {
    setSelectedTimeRange(range);
  };

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          colors={['#3b82f6']}
          tintColor={'#3b82f6'}
        />
      }
    >
      <MainHeader
        title="Admin Dashboard"
        subtitle="Admin real-time protection insights"
        rightActions={[
          {
            icon: 'menu',
            iconType: 'feather',
            onPress: toggleMenu
          }
        ]}
        style={{ paddingHorizontal: 0 }}
      />
      <View style={styles.timeRangeContainer}>
        {timeRanges.map((range) => (
          <TouchableOpacity
            key={range}
            style={[
              styles.timeRangeButton,
              selectedTimeRange === range && styles.timeRangeButtonActive,
            ]}
            onPress={() => handleTimeRangeChange(range)}
          >
            <Text
              style={[
                styles.timeRangeText,
                selectedTimeRange === range && styles.timeRangeTextActive,
              ]}
            >
              {range}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {error ? (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle-outline" size={24} color="#ef4444" />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Loading dashboard data...</Text>
        </View>
      ) : (
        <View style={styles.overallStatsContainer}>
          {overallStats.map((stat, index) => (
            <View key={index} style={styles.statCard}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
              <Text style={[styles.statChange, { color: stat.color }]}> {stat.change} </Text>
            </View>
          ))}
        </View>
      )}
      <View style={styles.chartContainer}>
        <View style={styles.chartHeader}>
          <Text style={styles.chartTitle}>Detection & Reports ({selectedTimeRange})</Text>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <LineChart
            data={{
              labels: detectionChartData.labels,
              datasets: [
                {
                  data: selectedData.detections,
                  color: (opacity = 1) => `rgba(1, 185, 127, ${opacity})`,
                  strokeWidth: 3,
                  withDots: true,
                  fillShadowGradient: 'rgba(16, 185, 129, 0.6)', // More vibrant green
                  fillShadowGradientOpacity: 0.6,
                },
                {
                  data: selectedData.reports,
                  color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
                  strokeWidth: 3,
                  withDots: true,
                  fillShadowGradient: 'rgba(59, 130, 246, 0.6)', // More vibrant blue
                  fillShadowGradientOpacity: 0.6,
                },
              ],
            }}
            width={Math.max((detectionChartData.labels.length * 80), width - 40)}
            height={220}
            chartConfig={{
              ...chartConfig,
              propsForBackgroundLines: { stroke: '#f3f4f6' },
              propsForLabels: { fontFamily: 'Poppins-Medium' },
            }}
            style={styles.chart}
            fromZero
            withDots={true}
            withShadow={true}
            withInnerLines={true}
            withHorizontalLabels={true}
            withVerticalLabels={true}
          />
        </ScrollView>
        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#01B97F' }]} />
            <Text style={styles.legendText}>Detections</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#3B82F6' }]} />
            <Text style={styles.legendText}>Reports</Text>
          </View>
        </View>
      </View>
      <View style={styles.menuContainer}>
        <Text style={styles.menuTitle}>Analytics Sections</Text>
        {menuOptions.map((option, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={() => navigation.navigate(option.screen)}
          >
            <View style={[styles.menuIcon, { backgroundColor: option.color }]}>
              <MaterialCommunityIcons name={option.icon} size={24} color={option.iconColor} />
            </View>
            <View style={styles.menuContent}>
              <Text style={styles.menuItemTitle}>{option.title}</Text>
              <Text style={styles.menuItemSubtitle}>{option.subtitle}</Text>
            </View>
            <Feather name="chevron-right" size={20} color="#9ca3af" />
          </TouchableOpacity>
        ))}
      </View>

      {/* New Analytics Sections */}
      <View style={styles.analyticsSectionContainer}>
        <Text style={styles.analyticsSectionTitle}>Top Flagged Content</Text>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#3b82f6" />
            <Text style={styles.loadingText}>Loading top flagged content...</Text>
          </View>
        ) : (
          <View style={styles.analyticsList}>
            {dashboardData.topFlaggedContent.map((item, index) => (
              <View key={item.id} style={styles.analyticsListItem}>
                <Text style={styles.analyticsListItemText}>{item.term}</Text>
                <Text style={styles.analyticsListItemValue}>{item.count}</Text>
              </View>
            ))}
          </View>
        )}
      </View>

      <View style={styles.analyticsSectionContainer}>
        <Text style={styles.analyticsSectionTitle}>Top Monitored Websites</Text>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#3b82f6" />
            <Text style={styles.loadingText}>Loading top monitored websites...</Text>
          </View>
        ) : (
          <View style={styles.analyticsList}>
            {dashboardData.topMonitoredWebsites.map((item, index) => (
              <View key={item.id} style={styles.analyticsListItem}>
                <Text style={styles.analyticsListItemText}>{item.url}</Text>
                <Text style={styles.analyticsListItemValue}>{item.issues}</Text>
              </View>
            ))}
          </View>
        )}
      </View>

      <View style={styles.analyticsSectionContainer}>
        <Text style={styles.analyticsSectionTitle}>Key User Activity</Text>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#3b82f6" />
            <Text style={styles.loadingText}>Loading key user activity...</Text>
          </View>
        ) : (
          <View style={styles.analyticsList}>
            {dashboardData.keyUserActivity.map((item, index) => (
              <View key={item.id} style={styles.analyticsListItem}>
                <Text style={styles.analyticsListItemText}>{item.user}</Text>
                <Text style={styles.analyticsListItemValue}>{item.actions}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>MURAi AI Insights</Text>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#3b82f6" />
            <Text style={styles.loadingText}>Loading insights...</Text>
          </View>
        ) : (
          <View style={styles.summaryItems}>
            {insightsData.map((insight, index) => (
              <View key={index} style={styles.summaryItem}>
                <MaterialCommunityIcons name={insight.icon} size={20} color={insight.color} />
                <Text style={styles.summaryText}>{insight.text}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
      <Modal
        visible={isMenuOpen}
        transparent={true}
        animationType="slide"
        onRequestClose={toggleMenu}
      >
        <TouchableOpacity style={styles.overlay} onPress={toggleMenu}>
          <View style={styles.bottomSheetContainer}>
            <View style={styles.bottomSheet}>
              <View style={styles.handleBar} />
              <View style={styles.menuHeader}>
                <Text style={styles.menuTitle}>Admin Dashboard</Text>
                <TouchableOpacity style={styles.closeButton} onPress={toggleMenu}>
                  <MaterialCommunityIcons name="close" size={24} color="#374151" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.menuScroll} showsVerticalScrollIndicator={false}>
                <View style={styles.menuSection}>
                  <Text style={styles.sectionTitle}>Analytics</Text>
                  {sideMenuItems.map((item, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.menuItem}
                      onPress={() => handleMenuAction(item.action)}
                    >
                      <View style={styles.menuItemIcon}>
                        <MaterialCommunityIcons name={item.icon} size={24} color="#374151" />
                      </View>
                      <View style={styles.menuItemContent}>
                        <Text style={styles.menuItemText}>{item.title}</Text>
                        <Text style={styles.menuItemSubtitle}>
                          {index === 0 ? 'Main dashboard overview' :
                            index === 1 ? 'Detection analytics and details' :
                            index === 2 ? 'Sites analytics and details' :
                            index === 3 ? 'Languages analytics and details' :
                            index === 4 ? 'Groups analytics and details' :
                            'Patterns over time analytics and details'}
                        </Text>
                      </View>
                      <MaterialCommunityIcons name="chevron-right" size={20} color="#9ca3af" />
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>
    </View>
        </TouchableOpacity>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    paddingHorizontal: 10,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 8,
  },
  timeRangeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  timeRangeButtonActive: {
    backgroundColor: '#01B97F',
  },
  timeRangeText: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#374151',
  },
  timeRangeTextActive: {
    fontFamily: 'Poppins-SemiBold',
    color: '#ffffff',
  },
  overallStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  statChange: {
    fontSize: 12,
    fontFamily: 'Poppins-Medium',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 10,
    fontFamily: 'Poppins-Medium',
    color: '#9ca3af',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  chartContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  chartTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
    marginBottom: 15,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    fontFamily: 'Poppins-Medium',
    color: '#6b7280',
  },
  menuContainer: {
    marginBottom: 30,
  },
  menuTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },

  menuContent: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 14,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#6b7280',
  },
  summaryContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    padding: 20,
    marginBottom: 40,
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  summaryTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  summaryItems: {
    gap: 12,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryText: {
    fontSize: 13,
    fontFamily: 'Poppins-Regular',
    color: '#6b7280',
    marginLeft: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    color: '#6b7280',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#ef4444',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    color: '#ef4444',
    marginTop: 8,
    textAlign: 'center',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  bottomSheetContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'transparent',
  },
  bottomSheet: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 10,
  },
  handleBar: {
    width: 40,
    height: 4,
    backgroundColor: '#e5e7eb',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 15,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  menuScroll: {
    flex: 1,
  },
  menuSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#374151',
    marginBottom: 12,
  },
  menuItemIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemText: {
    fontSize: 16,
    fontFamily: 'Poppins-Medium',
    color: '#111827',
    marginLeft: 16,
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
  },
  analyticsSectionContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  analyticsSectionTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  analyticsList: {
    gap: 10,
  },
  analyticsListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  analyticsListItemText: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#374151',
  },
  analyticsListItemValue: {
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    color: '#111827',
  },
});