import { Ionicons } from "@expo/vector-icons";
import * as AuthSession from 'expo-auth-session';
import { Link, useRouter } from "expo-router";
import * as WebBrowser from 'expo-web-browser';
import { useState } from "react";
import {
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StatusBar,
    Text,
    TouchableOpacity,
    View
} from "react-native";
import AppButton from "../../components/common/AppButton";
import AppInput from "../../components/common/AppInput";
import { BORDER_RADIUS, COLORS, SHADOWS, SPACING } from "../../constants/theme";
import { useAuth } from "../../context/AuthContext";

// Complete the auth session for web browser
WebBrowser.maybeCompleteAuthSession();

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");

  const { login, loginWithGoogle } = useAuth();
  const router = useRouter();

  // Google OAuth configuration
  const redirectUri = AuthSession.makeRedirectUri({
    scheme: 'murai-mobile',
    path: 'auth'
  });

  const handleLogin = async () => {
    if (!email || !password) {
      setError("Please fill in all fields");
      return;
    }

    setLoading(true);
    setError("");

    try {
      await login(email, password);
      router.replace("/(app)");
    } catch (err) {
      setError(err.message || "Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setGoogleLoading(true);
      setError("");

      const result = await loginWithGoogle();

      if (result.success) {
        console.log('✅ Google login successful');
        router.replace("/(app)");
      } else {
        setError("Google authentication failed");
      }
    } catch (err) {
      console.error('❌ Google login error:', err);
      setError(err.message || "Google sign-in failed. Please try again.");
    } finally {
      setGoogleLoading(false);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: COLORS.BG }}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.BG} />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.container}>
            {/* Header Section */}
            <View style={styles.header}>
              <Image
                source={require("../../../assets/images/logo.png")}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.title}>Welcome Back</Text>
              <Text style={styles.subtitle}>
                Sign in to continue to Murai
              </Text>
            </View>

            {/* Error Message */}
            {error ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={20} color={COLORS.ERROR} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : null}

            {/* Login Form */}
            <View style={styles.formContainer}>
              <AppInput
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  setError("");
                }}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                style={styles.input}
                leftIcon={
                  <Ionicons name="mail-outline" size={20} color={COLORS.TEXT_MUTED} />
                }
              />

              <AppInput
                value={password}
                onChangeText={(text) => {
                  setPassword(text);
                  setError("");
                }}
                placeholder="Enter your password"
                secureTextEntry={!showPassword}
                autoComplete="password"
                style={styles.input}
                leftIcon={
                  <Ionicons name="lock-closed-outline" size={20} color={COLORS.TEXT_MUTED} />
                }
                rightIcon={
                  <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                    <Ionicons
                      name={showPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color={COLORS.TEXT_MUTED}
                    />
                  </TouchableOpacity>
                }
              />

              {/* Forgot Password */}
              <TouchableOpacity style={styles.forgotPassword}>
                <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
              </TouchableOpacity>

              {/* Login Button */}
              <AppButton
                title="Sign In"
                onPress={handleLogin}
                loading={loading}
                style={styles.loginButton}
              />

              {/* Divider */}
              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>or</Text>
                <View style={styles.dividerLine} />
              </View>

              {/* Google Login Button */}
              <TouchableOpacity
                style={styles.googleButton}
                onPress={handleGoogleLogin}
                disabled={googleLoading}
              >
                <Ionicons name="logo-google" size={20} color={COLORS.TEXT_MAIN} />
                <Text style={styles.googleButtonText}>
                  {googleLoading ? "Signing in..." : "Continue with Google"}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Sign Up Link */}
            <View style={styles.signUpContainer}>
              <Text style={styles.signUpText}>Don't have an account? </Text>
              <Link href="/register" style={styles.signUpLink}>
                Sign Up
              </Link>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xxl,
    paddingBottom: SPACING.lg,
  },
  header: {
    alignItems: "center",
    marginBottom: SPACING.xxl,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: SPACING.lg,
    opacity: 0.95,
  },
  title: {
    color: COLORS.TEXT_MAIN,
    fontSize: 32,
    fontFamily: "Poppins-Bold",
    letterSpacing: 0.5,
    marginBottom: SPACING.xs,
    textAlign: "center",
  },
  subtitle: {
    color: COLORS.TEXT_MUTED,
    fontSize: 16,
    fontFamily: "Poppins-Regular",
    letterSpacing: 0.2,
    textAlign: "center",
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${COLORS.ERROR}15`,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: `${COLORS.ERROR}30`,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 14,
    fontFamily: "Poppins-Medium",
    marginLeft: SPACING.sm,
    flex: 1,
  },
  formContainer: {
    marginBottom: SPACING.xl,
  },
  input: {
    marginBottom: SPACING.md,
  },
  forgotPassword: {
    alignSelf: "flex-end",
    marginBottom: SPACING.lg,
  },
  forgotPasswordText: {
    color: COLORS.PRIMARY,
    fontSize: 14,
    fontFamily: "Poppins-Medium",
  },
  loginButton: {
    marginBottom: SPACING.lg,
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: SPACING.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.BORDER,
  },
  dividerText: {
    color: COLORS.TEXT_MUTED,
    fontSize: 14,
    fontFamily: "Poppins-Regular",
    marginHorizontal: SPACING.md,
  },
  googleButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: COLORS.CARD_BG,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    ...SHADOWS.sm,
  },
  googleButtonText: {
    color: COLORS.TEXT_MAIN,
    fontSize: 16,
    fontFamily: "Poppins-SemiBold",
    marginLeft: SPACING.sm,
  },
  signUpContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: "auto",
  },
  signUpText: {
    color: COLORS.TEXT_MUTED,
    fontSize: 16,
    fontFamily: "Poppins-Regular",
  },
  signUpLink: {
    color: COLORS.PRIMARY,
    fontSize: 16,
    fontFamily: "Poppins-SemiBold",
  },
};