import { ExpoConfig, ConfigContext } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: "Murai Mobile",
  slug: "murai-mobile",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/images/icon.png",
  userInterfaceStyle: "light",
  splash: {
    image: "./assets/images/splash.png",
    resizeMode: "contain",
    backgroundColor: "#ffffff"
  },
  assetBundlePatterns: [
    "**/*"
  ],
  ios: {
    supportsTablet: true,
    bundleIdentifier: "com.murai.mobile",
    infoPlist: {
      CFBundleURLTypes: [
        {
          CFBundleURLName: "murai-mobile",
          CFBundleURLSchemes: ["murai-mobile"]
        }
      ]
    }
  },
  android: {
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff"
    },
    package: "com.murai.mobile",
    intentFilters: [
      {
        action: "VIEW",
        autoVerify: true,
        data: [
          {
            scheme: "murai-mobile"
          }
        ],
        category: ["BROWSABLE", "DEFAULT"]
      }
    ]
  },
  web: {
    bundler: "metro",
    output: "static",
    favicon: "./assets/images/favicon.png"
  },
  plugins: [
    "expo-router",
    [
      "expo-font",
      {
        fonts: [
          "./assets/fonts/Poppins-Thin.ttf",
          "./assets/fonts/Poppins-ThinItalic.ttf",
          "./assets/fonts/Poppins-ExtraLight.ttf",
          "./assets/fonts/Poppins-ExtraLightItalic.ttf",
          "./assets/fonts/Poppins-Light.ttf",
          "./assets/fonts/Poppins-LightItalic.ttf",
          "./assets/fonts/Poppins-Regular.ttf",
          "./assets/fonts/Poppins-Italic.ttf",
          "./assets/fonts/Poppins-Medium.ttf",
          "./assets/fonts/Poppins-MediumItalic.ttf",
          "./assets/fonts/Poppins-SemiBold.ttf",
          "./assets/fonts/Poppins-SemiBoldItalic.ttf",
          "./assets/fonts/Poppins-Bold.ttf",
          "./assets/fonts/Poppins-BoldItalic.ttf",
          "./assets/fonts/Poppins-ExtraBold.ttf",
          "./assets/fonts/Poppins-ExtraBoldItalic.ttf",
          "./assets/fonts/Poppins-Black.ttf",
          "./assets/fonts/Poppins-BlackItalic.ttf"
        ]
      }
    ]
  ],
  scheme: "murai-mobile",
  experiments: {
    typedRoutes: true
  }
});
