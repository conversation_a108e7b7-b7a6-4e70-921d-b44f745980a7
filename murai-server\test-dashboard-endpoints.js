import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// Test user credentials (you'll need to replace with actual test user token)
const TEST_TOKEN = 'your_test_token_here';

async function testEndpoints() {
  console.log('Testing Dashboard Endpoints...\n');

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${TEST_TOKEN}`
  };

  // Test available years endpoint
  try {
    console.log('1. Testing /user-dashboard/available-years');
    const response = await fetch(`${BASE_URL}/user-dashboard/available-years`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', data);
    console.log('---\n');
  } catch (error) {
    console.log('Error:', error.message);
    console.log('---\n');
  }

  // Test overview endpoint
  try {
    console.log('2. Testing /user-dashboard/overview?timeRange=day');
    const response = await fetch(`${BASE_URL}/user-dashboard/overview?timeRange=day`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', data);
    console.log('---\n');
  } catch (error) {
    console.log('Error:', error.message);
    console.log('---\n');
  }

  // Test activity chart endpoint
  try {
    console.log('3. Testing /user-dashboard/activity-chart?timeRange=day');
    const response = await fetch(`${BASE_URL}/user-dashboard/activity-chart?timeRange=day`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', data);
    console.log('---\n');
  } catch (error) {
    console.log('Error:', error.message);
    console.log('---\n');
  }

  // Test year parameter
  try {
    console.log('4. Testing /user-dashboard/activity-chart?timeRange=year&year=2024');
    const response = await fetch(`${BASE_URL}/user-dashboard/activity-chart?timeRange=year&year=2024`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', data);
    console.log('---\n');
  } catch (error) {
    console.log('Error:', error.message);
    console.log('---\n');
  }
}

testEndpoints();
