import { TextInput, View } from "react-native";
import { BORDER_RADIUS, COLORS, SPACING } from "../../constants/theme";

export default function AppInput({
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  style = {},
  leftIcon,
  rightIcon,
  accessibilityLabel,
  accessibilityHint,
  ...props
}) {
  const hasIcons = leftIcon || rightIcon;

  if (hasIcons) {
    return (
      <View style={[inputContainerStyle, style]}>
        {leftIcon && (
          <View style={iconStyle}>
            {leftIcon}
          </View>
        )}
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={COLORS.TEXT_MUTED}
          secureTextEntry={secureTextEntry}
          accessibilityRole="text"
          accessibilityLabel={accessibilityLabel || placeholder}
          accessibilityHint={accessibilityHint || `Enter your ${placeholder?.toLowerCase() || 'text'}`}
          accessibilityState={{ disabled: props.editable === false }}
          style={[
            inputStyle,
            leftIcon && { paddingLeft: SPACING.xs },
            rightIcon && { paddingRight: SPACING.xs },
          ]}
          {...props}
        />
        {rightIcon && (
          <View style={iconStyle}>
            {rightIcon}
          </View>
        )}
      </View>
    );
  }

  return (
    <TextInput
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor={COLORS.TEXT_MUTED}
      secureTextEntry={secureTextEntry}
      accessibilityRole="text"
      accessibilityLabel={accessibilityLabel || placeholder}
      accessibilityHint={accessibilityHint || `Enter your ${placeholder?.toLowerCase() || 'text'}`}
      accessibilityState={{ disabled: props.editable === false }}
      style={[
        inputContainerStyle,
        style,
      ]}
      {...props}
    />
  );
}

const inputContainerStyle = {
  backgroundColor: COLORS.INPUT_BG,
  borderRadius: BORDER_RADIUS.md,
  borderWidth: 1,
  borderColor: COLORS.BORDER,
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: SPACING.md,
  paddingVertical: SPACING.md,
  minHeight: 50,
};

const inputStyle = {
  flex: 1,
  color: COLORS.TEXT_MAIN,
  fontSize: 16,
  fontFamily: "Poppins-Regular",
  paddingVertical: 0, // Remove default padding
};

const iconStyle = {
  paddingHorizontal: SPACING.xs,
};