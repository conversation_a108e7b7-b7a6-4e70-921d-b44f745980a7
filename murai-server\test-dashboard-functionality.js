import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';
import fetch from 'node-fetch';

dotenv.config();

const API_BASE_URL = 'http://localhost:3000/api';
const JWT_SECRET_KEY = process.env.JWT_SECRET_KEY;

// Create a test user token using the test user with data
const testUserToken = jwt.sign(
  {
    id: '6882f61cc328075458401c59', // Test user ID from the script output
    role: 'user',
    email: '<EMAIL>'
  },
  JWT_SECRET_KEY,
  { expiresIn: '1h' }
);

async function testDashboardEndpoints() {
  console.log('🧪 Testing Dashboard Functionality...\n');

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testUserToken}`
  };

  // Test 1: Available years endpoint
  try {
    console.log('1. Testing /user-dashboard/available-years');
    const response = await fetch(`${API_BASE_URL}/user-dashboard/available-years`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('---\n');
  } catch (error) {
    console.log('❌ Error:', error.message);
    console.log('---\n');
  }

  // Test 2: Overview endpoint with day
  try {
    console.log('2. Testing /user-dashboard/overview?timeRange=day');
    const response = await fetch(`${API_BASE_URL}/user-dashboard/overview?timeRange=day`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('---\n');
  } catch (error) {
    console.log('❌ Error:', error.message);
    console.log('---\n');
  }

  // Test 3: Activity chart endpoint with day
  try {
    console.log('3. Testing /user-dashboard/activity-chart?timeRange=day');
    const response = await fetch(`${API_BASE_URL}/user-dashboard/activity-chart?timeRange=day`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('---\n');
  } catch (error) {
    console.log('❌ Error:', error.message);
    console.log('---\n');
  }

  // Test 4: Activity chart with year parameter
  try {
    console.log('4. Testing /user-dashboard/activity-chart?timeRange=year&year=2024');
    const response = await fetch(`${API_BASE_URL}/user-dashboard/activity-chart?timeRange=year&year=2024`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('---\n');
  } catch (error) {
    console.log('❌ Error:', error.message);
    console.log('---\n');
  }

  // Test 5: Activity chart with overall year
  try {
    console.log('5. Testing /user-dashboard/activity-chart?timeRange=year&year=overall');
    const response = await fetch(`${API_BASE_URL}/user-dashboard/activity-chart?timeRange=year&year=overall`, {
      headers
    });
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('---\n');
  } catch (error) {
    console.log('❌ Error:', error.message);
    console.log('---\n');
  }
}

testDashboardEndpoints();
